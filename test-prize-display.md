# 奖品显示问题修复总结

## 问题描述
用户选择奖品后，奖品信息没有在表格中正确显示。

## 根本原因
1. **数据结构问题**：在 `generateExpandedTableData` 函数中，奖品信息存储在 `prize` 对象中，但表格列直接使用 `dataIndex` 从行数据根级别获取字段。

2. **字段映射问题**：表格期望的字段名（如 `lotteryName`, `lotteryType`, `prizeNum`）与奖品对象中的字段名不匹配。

3. **函数引用问题**：`editPrizeHandler` 函数中引用了不存在的变量和函数。

## 修复方案

### 1. 修复数据结构映射
在 `generateExpandedTableData` 函数中，将奖品字段提升到行数据的根级别：

```javascript
prizeList.forEach((prize, prizeIndex) => {
  expandedData.push({
    ...sku,
    skuIndex,
    prizeIndex,
    prize,
    // 将奖品字段提升到根级别，方便表格显示
    lotteryName: prize.lotteryName,
    lotteryType: prize.lotteryType,
    lotteryValue: prize.lotteryValue,
    prizeNum: prize.prizeNum,
    // ... 其他字段
  });
});
```

### 2. 修复表格列显示逻辑
将直接使用 `dataIndex` 的列改为使用 `cell` 函数，并正确处理没有奖品的情况：

```javascript
<Table.Column
  title="奖品"
  cell={(value, index, record) => {
    if (record.prizeIndex === -1) return '-';
    return record.lotteryName || '-';
  }}
/>
```

### 3. 修复函数引用问题
- 添加缺失的导入：`editPrize`, `activityCustomizeActivityMinusLotteryNum`
- 定义缺失的组件和常量：`WarningContent`, `initPrizeItem`
- 添加缺失的函数：`handleBackStock`
- 修复变量引用：将 `tableList` 改为 `demoSkuList`

### 4. 修复奖品选择逻辑
在 `addPrizeToSku` 函数中添加空值检查：

```javascript
if (res) {
  newDemoSkuList[skuIndex].prizeList.push(res);
  updateDemoSkuList(newDemoSkuList);
}
```

## 测试数据
使用提供的测试数据验证修复效果：
- 第一个SKU有一个奖品（lotteryName: "1111"）
- 第二、三个SKU的prizeList为空数组

## 预期结果
修复后，选择奖品应该能够：
1. 正确显示在表格的奖品列中
2. 显示正确的奖品类型
3. 显示正确的发放份数
4. 支持编辑和删除操作
