import { Message } from '@alifd/next';

import choosePhysicalGood from './choosePhysicalGood';
import chooseCoupon from './choosePrize/components/coupon/util';
import chooseShopPoint from './choosePrize/components/chooseShopPoint/chooseShopPoint';

import { PrizeTypeEnum } from '@/utils';
import choosePrize from './choosePrize/choosePrize';
import { prizeGetSkuInfo } from '@/api/vf';

/**
 * 编辑奖品 - 10201活动专用
 */
export default async function editPrize({ editPrizeInfo, disabledTabs, field, activityType, status }) {
    const prizeType = editPrizeInfo.lotteryType;

    // 实物奖品
    if (prizeType === PrizeTypeEnum.PRACTICALITY.value || prizeType === PrizeTypeEnum.PRACTICALITY_BY_ORDER.value) {
        try {
            //  根据skuCode获取奖品信息
            const res = await prizeGetSkuInfo({ skuCode: editPrizeInfo.lotteryValue });
            // 编辑的奖品信息，和获取的奖品信息进行合并
            console.log('res', res);
            const updatedRecord = {
                ...res,
                ...editPrizeInfo,
                benefitName: res?.benefitName || '',
                price: res?.amount,
            };
            const result = await choosePhysicalGood({
                record: updatedRecord,
                prizeType,
                field,
                activityType,
                status,
            });
            if (result === null) {
                return await choosePrize({
                    defaultActiveTab: prizeType,
                    disabledTabs,
                    activityType,
                    status,
                });
            }
            return result;
        } catch (error) {
            console.error('editPrize error:', error);
            Message.error('获取奖品信息失败');
            return null;
        }
    }

    // 优惠券 - 使用10201专用组件
    if (prizeType === PrizeTypeEnum.COUPON.value) {
        const data = await chooseCoupon({ record: editPrizeInfo, activityType, status });
        console.log(data, 'data');
        return data;
    }
    // 积分 - 使用10201专用组件
    if (prizeType === PrizeTypeEnum.MEMBER_POINT.value) {
        const result = await chooseShopPoint({ record: editPrizeInfo, field, activityType, status });
        if (result === null) {
            return await choosePrize({
                defaultActiveTab: prizeType,
                disabledTabs,
                activityType,
                status,
            });
        }
        return result;
    }

    return null;
}