import { Box, Button, Field, Form, Input, Radio, Divider, Message } from '@alifd/next';
import { getPrizeTypeLabel, PrizeTypeEnum } from '@/utils';
import NumberInput from '@/components/NumberInput/NumberInput';
import ImgUploadFromItem from '@/components/ImgUpload';
import { ACTIVITY_STATUS } from '@/utils/constant';

export default function Coupon({ onSelectPrize, record = null, activityType, status }: any) {
  console.log(record, 'record');

  const [activityStatus, operationType] = status || [];
  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const isNoStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
  const needDisable = isView;

  console.log(needDisable, 'needDisable');

  const shopCouponField = Field.useField({
    values: {
      // 奖品图
      showImage: record?.showImage ?? '',
      // 奖品类型 只读
      lotteryType: record?.lotteryType ?? PrizeTypeEnum.COUPON.value,
      // 奖品名称（可编辑）
      lotteryName: record?.lotteryName ?? '',
      // 单份积分值（可编辑）
      price: record?.price ?? 1,
      // 发放份数（积分的奖品数量 = 发放份数）
      prizeNum: record?.prizeNum ?? 1,
      // 优惠券ID
      lotteryValue: record?.lotteryValue ?? '',
      // 中奖概率
      probability: record?.probability || 0,
      // 每日限额 1 限制 2 不限制
      dayLimitType: record?.limitType || 2,
      // 每日限额份数
      dayLimitCount: record?.limitCount || 1,
      // 中奖限制
      awardLimitType: record?.totalLimitType || 2,
      // 中奖限制份数
      awardLimitCount: record?.totalLimitCount || 1,
      // 中奖弹窗引导按钮文案 未填写默认“我知道了”
      guideButtonText: record?.guideButtonText || '',
      // 引导按钮跳转链接  1 首页 2关闭
      guideButtonLink: record?.guideButtonLink || 2,
      // 关闭按钮触发引导跳转  1 开启 2 不开启"
      guideCloseButtonType: record?.guideCloseButtonType || 2,
    },
  });
  console.log(record?.lotteryName ?? '', 'record');

  const handleCancel = () => {
    onSelectPrize(null);
  };

  // const formatTime = (value: number) => {
  //   return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
  // };

  // 点击"保存"时进行表单校验，并组织最终返回的数据格式
  const handleSave = () => {
    shopCouponField.validate((errors, values: any) => {
      if (errors) return;
      // if (values.prizeNum > couponInfo.stockNumber) {
      //   Message.error(`发放份数不能超过可用库存 ${couponInfo.stockNumber}`);
      //   return;
      // }
      // 校验每日限额不得超过发放份数
      if (values.dayLimitType === 1 && values.dayLimitCount > values.prizeNum) {
        Message.error(`每日限额不能超过发放份数 ${values.prizeNum}`);
        return;
      }

      // 校验每人最多抽中次数不得超过发放份数
      if (values.awardLimitType === 1 && values.awardLimitCount > values.prizeNum) {
        Message.error(`每人最多抽中次数不能超过发放份数 ${values.prizeNum}`);
        return;
      }
      const result = {
        showImage: values.showImage,
        lotteryType: PrizeTypeEnum.COUPON.value,
        lotteryName: values.lotteryName, // 积分奖品名称
        price: values.price, // 价值等于单份积分值
        prizeNum: Number(values.prizeNum), // 奖品数量
        lotteryValue: String(values.lotteryValue),
        probability: values.probability,
        dayLimitType: values.dayLimitType,
        dayLimitCount: values.dayLimitCount,
        awardLimitType: values.awardLimitType,
        awardLimitCount: values.awardLimitCount,
        guideButtonText: values.guideButtonText,
        guideButtonLink: values.guideButtonLink,
        guideCloseButtonType: values.guideCloseButtonType,
      };
      onSelectPrize(result);
    });
  };
  return (
    <Box direction="column" spacing={16} padding={[20, 0, 0, 0]}>
      <div>
        <Form
          field={shopCouponField}
          className="edit-prize-form"
        >

          <Form.Item
            label="优惠券ID"
            required
            disabled={needDisable}
            name="lotteryValue"
            requiredMessage="请输入优惠券ID"
            pattern="^[0-9]+$"
            patternMessage="仅可输入数字"
          >
            <Input
              value={shopCouponField.getValue('lotteryValue')}
              onChange={val => shopCouponField.setValue('lotteryValue', val)}
            />
          </Form.Item>
          <ImgUploadFromItem
            name="showImage"
            label="奖品图"
            required
            requiredMessage="请上传奖品图"
            img={{
              value: shopCouponField.getValue('showImage'),
              width: 500,
              height: 500,
            }}
            onSuccess={url => {
              shopCouponField.setValue('showImage', url);
              shopCouponField.setError('showImage', '');
            }}
            onReset={() => shopCouponField.setValue('showImage', '')}
          />
          {/* 奖品类型（只读） */}
          <Form.Item label="奖品类型">
            <Input
              value={getPrizeTypeLabel(PrizeTypeEnum.COUPON.value)}
              disabled
              readOnly
            />
          </Form.Item>
          {/* 奖品名称 */}
          <Form.Item
            label="奖品名称"
            required
            requiredMessage="请输入奖品名称"
            pattern="^[\u4E00-\u9FA5A-Za-z0-9_\-/.]+$"
            patternMessage="不可输入特殊符号，支持中文、英文、数字及下划线"
            disabled={needDisable}
          >
            <Input
              name="lotteryName"
              maxLength={16}
              trim
              composition
              showLimitHint
              value={shopCouponField.getValue('lotteryName')}
              onChange={val => shopCouponField.setValue('lotteryName', val)}
            />
            <div style={{ fontSize: 12, color: 'var(--color-text-regular)', marginTop: 5 }}>奖品名称对外展示</div>
          </Form.Item>
          {/* 单份积分值 */}
          <Form.Item
            label="单份价值"
            required
            requiredMessage="请输入单份价值"
            disabled={needDisable}
          >
            <NumberInput
              name="price"
              min={1}
              value={shopCouponField.getValue('price')}
              onChange={val => shopCouponField.setValue('price', val)}
              style={{ width: '100%' }}
            />
          </Form.Item>
          {/* 发放份数 */}
          <Form.Item
            label="发放份数"
            required
            requiredMessage="请输入发放份数"
            min={1}
            disabled={needDisable}
          >
            <NumberInput
              name="prizeNum"
              min={1}
              value={shopCouponField.getValue('prizeNum')}
              onChange={val => shopCouponField.setValue('prizeNum', val)}
              style={{ width: '100%' }}
            />
          </Form.Item>
          {/* 转盘活动的特殊配置 */}
          {
            activityType === 'wheel' && (
              <>
                <Divider />
                <Form.Item
                  label="中奖概率"
                  required
                  disabled={isView || isEnded}
                  name={'probability'}
                  validator={(_, value: number) => {
                    if (value <= 0) {
                      return Promise.reject('请设置中奖概率');
                    }
                    return Promise.resolve();
                  }}
                >
                  <NumberInput
                    name="probability"
                    precision={3}
                    style={{ width: 150 }}
                    min={0.001}
                    max={99.999}
                    maxLength={7}
                    value={shopCouponField.getValue('probability')}
                    onChange={val => shopCouponField.setValue('probability', val)}
                  />
                </Form.Item>
                <Form.Item label="每日限额" required requiredMessage="请选择每日限额" disabled={needDisable}>
                  <Radio.Group
                    value={shopCouponField.getValue('dayLimitType')}
                    onChange={val => shopCouponField.setValue('dayLimitType', val)}
                  >
                    <Radio value={1}>限制</Radio>
                    <Radio value={2}>不限制</Radio>
                  </Radio.Group>
                </Form.Item>
                {
                  shopCouponField.getValue('dayLimitType') === 1 && (
                    <Form.Item label=" " disabled={needDisable}>
                      <NumberInput
                        style={{ width: 150, marginRight: 6 }}
                        min={1}
                        value={shopCouponField.getValue('dayLimitCount')}
                        onChange={val => shopCouponField.setValue('dayLimitCount', val)}
                        placeholder="请输入每日限额份数"
                      />份
                    </Form.Item>
                  )
                }
                <Form.Item label="中奖限制" required requiredMessage="请选择中奖限制" disabled={needDisable}>
                  <Radio.Group
                    value={shopCouponField.getValue('awardLimitType')}
                    onChange={val => shopCouponField.setValue('awardLimitType', val)}
                  >
                    <Radio value={1}>限制</Radio>
                    <Radio value={2}>不限制</Radio>
                  </Radio.Group>
                </Form.Item>
                {
                  shopCouponField.getValue('awardLimitType') === 1 && (
                    <Form.Item label=" " disabled={needDisable}>
                      每人最多抽中本奖品
                      <NumberInput
                        style={{ width: 150, margin: '0 6px' }}
                        min={1}
                        value={shopCouponField.getValue('awardLimitCount')}
                        onChange={val => shopCouponField.setValue('awardLimitCount', val)}
                        placeholder="请输入中奖限制份数"
                      />次
                    </Form.Item>
                  )
                }
                <Divider />
                <Form.Item label="中奖弹窗引导按钮文案" disabled={needDisable}>
                  <Input
                    name="guideButtonText"
                    placeholder="未填写默认为：我知道了"
                    maxLength={5}
                    showLimitHint
                    value={shopCouponField.getValue('guideButtonText')}
                    onChange={val => shopCouponField.setValue('guideButtonText', val)}
                  />
                </Form.Item>
                <Form.Item label="引导按钮跳转链接" disabled={needDisable}>
                  <Radio.Group
                    value={shopCouponField.getValue('guideButtonLink')}
                    onChange={val => shopCouponField.setValue('guideButtonLink', val)}
                  >
                    <Radio value={2}>关闭</Radio>
                    <Radio value={1}>首页</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item label="关闭按钮触发引导跳转" disabled={needDisable}>
                  <Radio.Group
                    value={shopCouponField.getValue('guideCloseButtonType')}
                    onChange={val => shopCouponField.setValue('guideCloseButtonType', val)}
                  >
                    <Radio value={2}>不开启</Radio>
                    <Radio value={1}>开启</Radio>
                  </Radio.Group>
                </Form.Item>
              </>
            )
          }
          {/* 底部按钮 */}
          <Box
            direction="row"
            justify="center"
            spacing={16}
            margin={[20, 0, 0, 0]}
          >
            <Button onClick={handleCancel}>{'取消'}</Button>
            <Button
              type="primary"
              onClick={handleSave}
            >
              保存
            </Button>
          </Box>
        </Form>
      </div>
    </Box>
  );
}

