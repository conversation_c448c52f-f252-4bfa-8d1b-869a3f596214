export enum ModuleType {
  BASE = 'base',
  THRESHOLD = 'threshold',
  ORDER = 'order',
  DEMO_ORIGINAL_FILE = 'demoOriginalFile',
  DEMO_SKU_LIST = 'demoSkuList',
  REPURCHASE_PRIZE_LIST = 'repurchasePrizeList',
  REPURCHASE_SKU_LIST = 'repurchaseSkuList',
  RULE = 'rule',
  SHARE = 'share',
  RECOMMEND_GOODS = 'recommendGoods',
}
// Action类型定义
type ActionType =
  | 'UPDATE_BASE'
  | 'UPDATE_THRESHOLD'
  | 'UPDATE_ORDER'
  | 'UPDATE_DEMO_ORIGINAL_FILE'
  | 'UPDATE_DEMO_SKU_LIST'
  | 'UPDATE_REPURCHASE_PRIZE_LIST'
  | 'UPDATE_REPURCHASE_SKU_LIST'
  | 'UPDATE_RULE'
  | 'UPDATE_SHARE'
  | 'UPDATE_RECOMMEND_GOODS'
  | 'UPDATE_DECORATE'
  | 'VALIDATE_MODULE'
  | 'UPDATE_EXTRA'
  | 'CLEAR_ERRORS'
  | 'INIT_MODULE';

export interface Action {
  type: ActionType;
  payload?: any;
  module?: ModuleType;
}

// 基础信息模块状态定义
export interface BaseInfoState {
  activityType: number;
  activityName: string;
  startTime: string;
  endTime: string;
  templateCode: number;
  activityId?: string;
}
export interface ExtraState {
  operationType: 'edit' | 'add' | 'copy' | 'view';
  originalEndTime: string;
  // 活动状态 1未开始 2进行中 3已结束
  activityStatus: number;
  activityUrl: string;
}

export interface ThresholdState {
  thresholdType: number;
  thresholdInfo: any;
  // 是否限制会员 1-是 0 -否
  isMember: number;
}

export interface OrderState {
  // 前置订单前推天数
  days: number;
  // 前置正装sku
  beforeSkuList: [];
  // 前置订单是否延迟发奖 0-不延迟 1-延迟
  isDelayedDisttributionBefore: number;
  // 前置订单延迟发奖天数
  awardDaysBefore: number;
}

// 小听（中听）上传文件
export interface DemoOriginalFileState {
  demoOriginalFile: [];
  demoFileName: string;
}

// 小听sku和奖品
export interface DemoSkuListState {
  // 品线
  goodsLine: string;
  // 使用 sectionNum 作为段数
  sectionNum: number;
  // 主图
  skuMainPicture: string;
  // 奖品主名称
  prizeMainName: string;
  // sku名称
  skuName: string;
  skuId: string;
  // 每日限额
  dayLimitCount: string;
  // 奖品列表
  prizeList: any[];
}

// export type DemoSkuListState = any[];

export type PrizeState = any[];

// 复购sku
export interface RepurchaseSkuListState {
  skuId: string;
  skuName: string;
  // 段数
  sectionNum: number;
  // 品线
  goodsLine: string;
  // 默认兜底图片
  skuMainPicture: string;
}

export interface SkuItem {
  numIid: string;
  name: string;
  picUrl: string;
  sellNum: number;
}

export interface ShareState {
  shareTitle: string;
  shareContent: string;
  mpImg: string;
}

export interface AppState {
  extra: ExtraState;
  base: BaseInfoState;
  threshold: ThresholdState;
  order: OrderState;
  demoOriginalFile: DemoOriginalFileState;
  demoSkuList: DemoSkuListState[];
  repurchasePrizeList: PrizeState[];
  repurchaseSkuList: RepurchaseSkuListState[];
  rule: string;
  decorate: string;
  share: ShareState;
  recommendGoods: SkuItem[];
  errors: Record<ModuleType, string[]>;
}
